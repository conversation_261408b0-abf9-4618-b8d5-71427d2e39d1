'use client';

import { Order } from '@/lib/definitions/order';
import { Button, Modal } from '@ourtrip/ui';
import { X, XCircle } from '@phosphor-icons/react';

const OrderCancelModal = ({
  order,
  open,
  onClose,
  onConfirm,
  isLoading = false
}: {
  order: Order;
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading?: boolean;
}) => {
  return (
    <Modal isOpen={open} onClose={onClose}>
      <div className='flex flex-col gap-3 p-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <XCircle className='text-primary-900' />
            <p className='text-gray-500'>Confirmar cancelamento</p>
          </div>
          <X onClick={onClose} />
        </div>
        <div className='flex flex-col px-1 w-[400px]'>
          <p className='text-primary-900 font-medium '>
            Você tem certeza que deseja solicitar o cancelamento deste pedido?
          </p>
          <span className='text-sm text-gray-500 font-normal mt-2'>
            {order?.cancellationInfo?.infos?.map(info => (
              <>
                <p key={info}>{info}</p>
              </>
            ))}
          </span>
        </div>
        <div className='flex justify-end gap-2'>
          <Button className='w-min' onClick={onClose} color='white'>
            Não
          </Button>
          <Button
            className='w-min'
            onClick={onConfirm}
            color='danger'
            loading={isLoading}
          >
            Sim, cancelar
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default OrderCancelModal;
