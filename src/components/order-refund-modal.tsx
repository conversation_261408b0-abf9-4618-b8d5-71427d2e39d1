'use client';

import { useState } from 'react';
import { Order } from '@/lib/definitions/order';
import { Button, Input, Modal } from '@ourtrip/ui';
import { ArrowArcLeft, X } from '@phosphor-icons/react';
import { useForm } from 'react-hook-form';

const OrderRefundModal = ({
  open,
  onClose,
  onConfirm,
  isLoading = false
}: {
  order: Order;
  open: boolean;
  onClose: () => void;
  onConfirm: (amount: number) => void;
  isLoading?: boolean;
}) => {
  const { register, handleSubmit } = useForm<{ amount: number }>({
    defaultValues: { amount: 0 }
  });

  const submit = async (data: { amount: number }) => {
    onConfirm(data.amount);
  };

  return (
    <Modal isOpen={open} onClose={onClose}>
      <form className='flex flex-col gap-3 p-4' onSubmit={handleSubmit(submit)}>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <ArrowArcLeft className='text-primary-900' />
            <p className='text-gray-500'>Realizar reembolso</p>
          </div>
          <X onClick={onClose} />
        </div>
        <div className='flex flex-col px-1 w-[400px]'>
          <p className='text-primary-900 font-medium'>
            Você tem certeza que deseja solicitar o reembolso deste pedido?
          </p>
          <Input
            color='gray'
            placeholder='Valor'
            {...register('amount', { required: true })}
            hidden
          />
        </div>
        <div className='flex justify-end gap-2'>
          <Button className='w-min' onClick={onClose} color='white'>
            Não
          </Button>
          <Button
            type='submit'
            className='w-min'
            color='danger'
            loading={isLoading}
          >
            Sim, solicitar reembolso
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default OrderRefundModal;
